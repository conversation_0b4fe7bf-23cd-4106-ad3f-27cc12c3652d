{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Install mergoo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install mergoo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build Mixture of Adapters (MoE on LoRAs) Checkpoint\n", "\n", "### Selecting Experts:\n", "The advantage of mergoo is that you can leverage the fine-tuned experts, which excel in their respective domains. In this notebook, we merge three LoRA fine-tuned experts into a unified mixture-of-adapters architecture, which can be later fine-tuned.\n", "\n", "We choose experts that are trained on customer support datasets: \n", "- [predibase/customer_support](https://huggingface.co/predibase/customer_support)\n", "- [predibase/customer_support_accounts](https://huggingface.co/predibase/customer_support_accounts)\n", "- [predibase/customer_support_orders](https://huggingface.co/predibase/customer_support_orders)\n", "\n", "### Preparing Config:\n", "- `model_type`: ```llama/mistral/bert```. This is the base model family of the experts. At the moment, all the experts should come from the same base model family.\n", "- `num_experts_per_tok`: Total number of active experts at each step. These experts are selected sparsely.\n", "- `base_model`: Model id for the base model. Make sure that all the LaRA experts are having an common base model, since the mixture-of-adapters configuration trains a router on top of LoRA, while keeping rest all of the layers frozen.   \n", "- `experts`: List of dictionaries of seed LoRA models for merging. For each expert, `model_id` is mandatory. The model_id can be either a local path or a Huggingface model id."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MoE Layer Index : [*]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "725b2ff03b9c4fd8b489bdbaf399c9cf", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 675/675 [00:00<00:00, 300962.60it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["count_averaged_layers : 227\n", "count_router_layers : 448\n", "count_total_router_layers : 448\n", "The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe/model.safetensors.index.json.\n", "checkpoint saved at data/mistral_lora_moe\n"]}], "source": ["import torch\n", "from mergoo.compose_experts import ComposeExperts\n", "\n", "model_id = \"data/mistral_lora_moe\"\n", "config = {\n", "    \"model_type\": \"mistral\",\n", "    \"num_experts_per_tok\": 2,\n", "    \"base_model\": \"mistralai/Mistral-7B-v0.1\",\n", "    \"experts\": [\n", "        {\n", "            \"expert_name\": \"adapter_1\", \n", "            \"model_id\": \"predibase/customer_support\"\n", "        },\n", "        {\n", "            \"expert_name\": \"adapter_2\", \n", "            \"model_id\": \"predibase/customer_support_accounts\"\n", "        },\n", "        {\n", "            \"expert_name\": \"adapter_3\", \n", "            \"model_id\": \"predibase/customer_support_orders\"\n", "        }\n", "    ],\n", "}\n", "# create checkpoint\n", "expertmerger = ComposeExperts( config, torch_dtype=torch.bfloat16 )\n", "expertmerger.compose()\n", "expertmerger.save_checkpoint(  \"data/mistral_lora_moe\" )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Training\n", "\n", "Now that we have created the checkpoint of mrged model, all the layers are pretrained except for the gating/routing layers that are added. The routing layer selects the top K experts (K=2, here). We support HuggingFace trainers: <PERSON><PERSON>, SFTrainer.  \n", "In this example, we are using ```Python_code_instructions_18k_alpaca``` dataset for finetuning. We will train only the router layers, keeping all the other layers frozen."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d548ba24f1de458e936296f225b4bcbf", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe and are newly initialized: ['model.layers.0.self_attn.q_proj.gate.weight', 'model.layers.0.self_attn.v_proj.gate.weight', 'model.layers.1.self_attn.q_proj.gate.weight', 'model.layers.1.self_attn.v_proj.gate.weight', 'model.layers.10.self_attn.q_proj.gate.weight', 'model.layers.10.self_attn.v_proj.gate.weight', 'model.layers.11.self_attn.q_proj.gate.weight', 'model.layers.11.self_attn.v_proj.gate.weight', 'model.layers.12.self_attn.q_proj.gate.weight', 'model.layers.12.self_attn.v_proj.gate.weight', 'model.layers.13.self_attn.q_proj.gate.weight', 'model.layers.13.self_attn.v_proj.gate.weight', 'model.layers.14.self_attn.q_proj.gate.weight', 'model.layers.14.self_attn.v_proj.gate.weight', 'model.layers.15.self_attn.q_proj.gate.weight', 'model.layers.15.self_attn.v_proj.gate.weight', 'model.layers.16.self_attn.q_proj.gate.weight', 'model.layers.16.self_attn.v_proj.gate.weight', 'model.layers.17.self_attn.q_proj.gate.weight', 'model.layers.17.self_attn.v_proj.gate.weight', 'model.layers.18.self_attn.q_proj.gate.weight', 'model.layers.18.self_attn.v_proj.gate.weight', 'model.layers.19.self_attn.q_proj.gate.weight', 'model.layers.19.self_attn.v_proj.gate.weight', 'model.layers.2.self_attn.q_proj.gate.weight', 'model.layers.2.self_attn.v_proj.gate.weight', 'model.layers.20.self_attn.q_proj.gate.weight', 'model.layers.20.self_attn.v_proj.gate.weight', 'model.layers.21.self_attn.q_proj.gate.weight', 'model.layers.21.self_attn.v_proj.gate.weight', 'model.layers.22.self_attn.q_proj.gate.weight', 'model.layers.22.self_attn.v_proj.gate.weight', 'model.layers.23.self_attn.q_proj.gate.weight', 'model.layers.23.self_attn.v_proj.gate.weight', 'model.layers.24.self_attn.q_proj.gate.weight', 'model.layers.24.self_attn.v_proj.gate.weight', 'model.layers.25.self_attn.q_proj.gate.weight', 'model.layers.25.self_attn.v_proj.gate.weight', 'model.layers.26.self_attn.q_proj.gate.weight', 'model.layers.26.self_attn.v_proj.gate.weight', 'model.layers.27.self_attn.q_proj.gate.weight', 'model.layers.27.self_attn.v_proj.gate.weight', 'model.layers.28.self_attn.q_proj.gate.weight', 'model.layers.28.self_attn.v_proj.gate.weight', 'model.layers.29.self_attn.q_proj.gate.weight', 'model.layers.29.self_attn.v_proj.gate.weight', 'model.layers.3.self_attn.q_proj.gate.weight', 'model.layers.3.self_attn.v_proj.gate.weight', 'model.layers.30.self_attn.q_proj.gate.weight', 'model.layers.30.self_attn.v_proj.gate.weight', 'model.layers.31.self_attn.q_proj.gate.weight', 'model.layers.31.self_attn.v_proj.gate.weight', 'model.layers.4.self_attn.q_proj.gate.weight', 'model.layers.4.self_attn.v_proj.gate.weight', 'model.layers.5.self_attn.q_proj.gate.weight', 'model.layers.5.self_attn.v_proj.gate.weight', 'model.layers.6.self_attn.q_proj.gate.weight', 'model.layers.6.self_attn.v_proj.gate.weight', 'model.layers.7.self_attn.q_proj.gate.weight', 'model.layers.7.self_attn.v_proj.gate.weight', 'model.layers.8.self_attn.q_proj.gate.weight', 'model.layers.8.self_attn.v_proj.gate.weight', 'model.layers.9.self_attn.q_proj.gate.weight', 'model.layers.9.self_attn.v_proj.gate.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["# load the composed checkkpoint\n", "import torch\n", "from mergoo.models.modeling_mistral import MistralForCausalLM\n", "\n", "model = MistralForCausalLM.from_pretrained(\n", "    \"data/mistral_lora_moe\",\n", "    device_map=\"auto\",\n", "    torch_dtype=torch.bfloat16,\n", ")# 'gate' / router layers are untrained hence loaded warning would appeare for them"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(739, 96)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# train only router (gating) layers\n", "n_weights, n_router_weights  = 0,0\n", "for name, weight in model.named_parameters():\n", "    if \"gate\" in name:\n", "        weight.requires_grad_(True)\n", "        n_router_weights += 1\n", "    else:\n", "        weight.requires_grad_(False)\n", "    n_weights += 1\n", "n_weights, n_router_weights"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import datasets\n", "import random\n", "\n", "dataset = datasets.load_dataset(\"bitext/Bitext-customer-support-llm-chatbot-training-dataset\")['train']\n", "# 90% train, 10% test + validation\n", "train_testvalid = dataset.train_test_split(test_size=0.1)\n", "# Split the 10% test + valid in 0.7 test, half valid\n", "test_valid = train_testvalid['test'].train_test_split(test_size=0.7)\n", "# gather everyone if you want to have a single DatasetDict\n", "dataset = datasets.DatasetDict({\n", "    'train': train_testvalid['train'],\n", "    'test': test_valid['test'],\n", "    'val': test_valid['train']})"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['flags', 'instruction', 'category', 'intent', 'response'],\n", "        num_rows: 24184\n", "    })\n", "    test: Dataset({\n", "        features: ['flags', 'instruction', 'category', 'intent', 'response'],\n", "        num_rows: 1882\n", "    })\n", "    val: Dataset({\n", "        features: ['flags', 'instruction', 'category', 'intent', 'response'],\n", "        num_rows: 806\n", "    })\n", "})"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def formatting_prompts_func(example):\n", "    output_texts = []\n", "    for i in range(len(example['instruction'])):\n", "        text = f\"### Question: {example['instruction'][i]}\\n ### Answer: {example['response'][i]}\"\n", "        output_texts.append(text)\n", "    return output_texts"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4532f5e9715e474b961159f90f8bba22", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/24184 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "33c967e037cb4060a4fc6257d10503f8", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/806 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/envs/pytorch/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:317: UserWarning: You passed a tokenizer with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `tokenizer.padding_side = 'right'` to your code.\n", "  warnings.warn(\n"]}], "source": ["from trl import SFTTrainer\n", "from transformers import TrainingArguments\n", "\n", "trainer_args = TrainingArguments(\n", "    output_dir= \"data/mistral_cs_lora_moe\",\n", "    per_device_train_batch_size = 1,\n", "    per_device_eval_batch_size = 1, \n", "    learning_rate= 1e-5,\n", "    save_total_limit=1,\n", "    num_train_epochs=1,\n", "    eval_steps= 5000,\n", "    logging_strategy=\"steps\",\n", "    logging_steps= 25,\n", "    gradient_accumulation_steps=4,\n", "    bf16=True\n", ")\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model,\n", "    args= trainer_args,\n", "    train_dataset= dataset['train'],\n", "    eval_dataset= dataset['val'],\n", "    formatting_func=formatting_prompts_func,\n", "    max_seq_length=512\n", ")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='117' max='6046' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 117/6046 02:38 < 2:16:31, 0.72 it/s, Epoch 0.02/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.056200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.885700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>75</td>\n", "      <td>0.842000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>0.827700</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer.train()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}