{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Install Mergoo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install mergoo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Mergoo-MOE Checkpoint\n", "\n", "**Selecting Experts:**  \n", "\n", "You can easily merge phi3-based LLM experts. In the following, we have merged two fine-tuned experts:\n", "\n", "- [microsoft/Phi-3-mini-128k-instruct](https://huggingface.co/microsoft/Phi-3-mini-128k-instruct): Base generic Phi3 model.\n", "- [RDson/Phi-3-mini-code-finetune-128k-instruct-v1](https://huggingface.co/RDson/Phi-3-mini-code-finetune-128k-instruct-v1): Phi3-based LLM model, fine-tuned on instrcution-based dataset for coding.  \n", "- [<PERSON><PERSON><PERSON><PERSON>/Phi-3-mini-128k-instruct_function](https://huggingface.co/NickyNicky/Phi-3-mini-128k-instruct_function): fine-tuned Phi3-based model for function calling.  \n", "\n", "**Preparing Config:**\n", "- `model_type`: llama/mistral/bert/phi3. This is the base model family of the experts. At the moment, all the experts should come from the same base model family.\n", "- `num_experts_per_tok`: Total number of active experts at each step. These experts are selected sparsely.\n", "- `experts`: List of dictionaries of seed models that would get merged. For each expert, `model_id` is mandatory. The model_id can be either a local path or a Huggingface model id.\n", "- `router_layers`: These are the layer names that would be replaced with MOE layers. Weights of the rest of the layers are aggregated using averaging. In the future, we will support multiple aggregation methods from MergeKit.\n", "- `router_layers_index`: List of indexes. These are the indexes of transformer blocks, layers of these index would be converted to MOE. Default `router_layers_index` is empty meaning the MOE conversion gets applied on all the layers, given that `router_layers` identifier matches. `[None]` can be used when no MOE layer should be kept following the [BTM](https://arxiv.org/abs/2208.03306) architecture."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from mergoo.compose_experts import ComposeExperts\n", "\n", "model_id =  \"data/checkpoint_demo\"\n", "config = \\\n", "{\n", "    \"model_type\": \"phi3\",\n", "    \"num_experts_per_tok\": 2,\n", "    \"experts\":[\n", "        {\n", "            \"expert_name\" : \"base_expert\",\n", "            \"model_id\" : \"microsoft/Phi-3-mini-128k-instruct\"\n", "        },\n", "        {\n", "            \"expert_name\" : \"expert_1\",\n", "            \"model_id\" : \"RDson/Phi-3-mini-code-finetune-128k-instruct-v1\",\n", "        },\n", "        {\n", "            \"expert_name\" : \"expert_2\",\n", "            \"model_id\" : \"NickyNicky/Phi-3-mini-128k-instruct_function\",\n", "        },\n", "    ],\n", "    \"router_layers\":[\n", "        \"gate_up_proj\",\n", "        \"down_proj\",\n", "    ],\n", "}\n", "# create checkpoint\n", "expertmerger = ComposeExperts( config, torch_dtype=torch.float16 )\n", "expertmerger.compose()\n", "expertmerger.save_checkpoint(model_id)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training\n", "\n", "Now that we have created an MOE checkpoint, all the layers of this model are pretrained except for the gating/routing layers that we added. The routing layer selects the top K experts, in our case K=2. We support HuggingFace trainers: <PERSON>er, SFTrainer. In this example, we are using the Python_code_instructions_18k_alpaca dataset for finetuning. We will train only the router layers, keeping all the other layers frozen."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of the model checkpoint at data/checkpoint_demo were not used when initializing Phi3ForCausalLM: ['model.layers.0.mlp.down_proj.weight', 'model.layers.0.mlp.gate_up_proj.weight', 'model.layers.1.mlp.down_proj.weight', 'model.layers.1.mlp.gate_up_proj.weight', 'model.layers.10.mlp.down_proj.weight', 'model.layers.10.mlp.gate_up_proj.weight', 'model.layers.11.mlp.down_proj.weight', 'model.layers.11.mlp.gate_up_proj.weight', 'model.layers.12.mlp.down_proj.weight', 'model.layers.12.mlp.gate_up_proj.weight', 'model.layers.13.mlp.down_proj.weight', 'model.layers.13.mlp.gate_up_proj.weight', 'model.layers.14.mlp.down_proj.weight', 'model.layers.14.mlp.gate_up_proj.weight', 'model.layers.15.mlp.down_proj.weight', 'model.layers.15.mlp.gate_up_proj.weight', 'model.layers.16.mlp.down_proj.weight', 'model.layers.16.mlp.gate_up_proj.weight', 'model.layers.17.mlp.down_proj.weight', 'model.layers.17.mlp.gate_up_proj.weight', 'model.layers.18.mlp.down_proj.weight', 'model.layers.18.mlp.gate_up_proj.weight', 'model.layers.19.mlp.down_proj.weight', 'model.layers.19.mlp.gate_up_proj.weight', 'model.layers.2.mlp.down_proj.weight', 'model.layers.2.mlp.gate_up_proj.weight', 'model.layers.20.mlp.down_proj.weight', 'model.layers.20.mlp.gate_up_proj.weight', 'model.layers.21.mlp.down_proj.weight', 'model.layers.21.mlp.gate_up_proj.weight', 'model.layers.22.mlp.down_proj.weight', 'model.layers.22.mlp.gate_up_proj.weight', 'model.layers.23.mlp.down_proj.weight', 'model.layers.23.mlp.gate_up_proj.weight', 'model.layers.24.mlp.down_proj.weight', 'model.layers.24.mlp.gate_up_proj.weight', 'model.layers.25.mlp.down_proj.weight', 'model.layers.25.mlp.gate_up_proj.weight', 'model.layers.26.mlp.down_proj.weight', 'model.layers.26.mlp.gate_up_proj.weight', 'model.layers.27.mlp.down_proj.weight', 'model.layers.27.mlp.gate_up_proj.weight', 'model.layers.28.mlp.down_proj.weight', 'model.layers.28.mlp.gate_up_proj.weight', 'model.layers.29.mlp.down_proj.weight', 'model.layers.29.mlp.gate_up_proj.weight', 'model.layers.3.mlp.down_proj.weight', 'model.layers.3.mlp.gate_up_proj.weight', 'model.layers.30.mlp.down_proj.weight', 'model.layers.30.mlp.gate_up_proj.weight', 'model.layers.31.mlp.down_proj.weight', 'model.layers.31.mlp.gate_up_proj.weight', 'model.layers.4.mlp.down_proj.weight', 'model.layers.4.mlp.gate_up_proj.weight', 'model.layers.5.mlp.down_proj.weight', 'model.layers.5.mlp.gate_up_proj.weight', 'model.layers.6.mlp.down_proj.weight', 'model.layers.6.mlp.gate_up_proj.weight', 'model.layers.7.mlp.down_proj.weight', 'model.layers.7.mlp.gate_up_proj.weight', 'model.layers.8.mlp.down_proj.weight', 'model.layers.8.mlp.gate_up_proj.weight', 'model.layers.9.mlp.down_proj.weight', 'model.layers.9.mlp.gate_up_proj.weight']\n", "- This IS expected if you are initializing Phi3ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing Phi3ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n", "Some weights of Phi3ForCausalLM were not initialized from the model checkpoint at data/checkpoint_demo and are newly initialized: ['model.layers.0.mlp.down_proj.experts.0.weight', 'model.layers.0.mlp.down_proj.experts.1.weight', 'model.layers.0.mlp.down_proj.experts.2.weight', 'model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_up_proj.experts.0.weight', 'model.layers.0.mlp.gate_up_proj.experts.1.weight', 'model.layers.0.mlp.gate_up_proj.experts.2.weight', 'model.layers.0.mlp.gate_up_proj.gate.weight', 'model.layers.1.mlp.down_proj.experts.0.weight', 'model.layers.1.mlp.down_proj.experts.1.weight', 'model.layers.1.mlp.down_proj.experts.2.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_up_proj.experts.0.weight', 'model.layers.1.mlp.gate_up_proj.experts.1.weight', 'model.layers.1.mlp.gate_up_proj.experts.2.weight', 'model.layers.1.mlp.gate_up_proj.gate.weight', 'model.layers.10.mlp.down_proj.experts.0.weight', 'model.layers.10.mlp.down_proj.experts.1.weight', 'model.layers.10.mlp.down_proj.experts.2.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_up_proj.experts.0.weight', 'model.layers.10.mlp.gate_up_proj.experts.1.weight', 'model.layers.10.mlp.gate_up_proj.experts.2.weight', 'model.layers.10.mlp.gate_up_proj.gate.weight', 'model.layers.11.mlp.down_proj.experts.0.weight', 'model.layers.11.mlp.down_proj.experts.1.weight', 'model.layers.11.mlp.down_proj.experts.2.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_up_proj.experts.0.weight', 'model.layers.11.mlp.gate_up_proj.experts.1.weight', 'model.layers.11.mlp.gate_up_proj.experts.2.weight', 'model.layers.11.mlp.gate_up_proj.gate.weight', 'model.layers.12.mlp.down_proj.experts.0.weight', 'model.layers.12.mlp.down_proj.experts.1.weight', 'model.layers.12.mlp.down_proj.experts.2.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_up_proj.experts.0.weight', 'model.layers.12.mlp.gate_up_proj.experts.1.weight', 'model.layers.12.mlp.gate_up_proj.experts.2.weight', 'model.layers.12.mlp.gate_up_proj.gate.weight', 'model.layers.13.mlp.down_proj.experts.0.weight', 'model.layers.13.mlp.down_proj.experts.1.weight', 'model.layers.13.mlp.down_proj.experts.2.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_up_proj.experts.0.weight', 'model.layers.13.mlp.gate_up_proj.experts.1.weight', 'model.layers.13.mlp.gate_up_proj.experts.2.weight', 'model.layers.13.mlp.gate_up_proj.gate.weight', 'model.layers.14.mlp.down_proj.experts.0.weight', 'model.layers.14.mlp.down_proj.experts.1.weight', 'model.layers.14.mlp.down_proj.experts.2.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_up_proj.experts.0.weight', 'model.layers.14.mlp.gate_up_proj.experts.1.weight', 'model.layers.14.mlp.gate_up_proj.experts.2.weight', 'model.layers.14.mlp.gate_up_proj.gate.weight', 'model.layers.15.mlp.down_proj.experts.0.weight', 'model.layers.15.mlp.down_proj.experts.1.weight', 'model.layers.15.mlp.down_proj.experts.2.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_up_proj.experts.0.weight', 'model.layers.15.mlp.gate_up_proj.experts.1.weight', 'model.layers.15.mlp.gate_up_proj.experts.2.weight', 'model.layers.15.mlp.gate_up_proj.gate.weight', 'model.layers.16.mlp.down_proj.experts.0.weight', 'model.layers.16.mlp.down_proj.experts.1.weight', 'model.layers.16.mlp.down_proj.experts.2.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_up_proj.experts.0.weight', 'model.layers.16.mlp.gate_up_proj.experts.1.weight', 'model.layers.16.mlp.gate_up_proj.experts.2.weight', 'model.layers.16.mlp.gate_up_proj.gate.weight', 'model.layers.17.mlp.down_proj.experts.0.weight', 'model.layers.17.mlp.down_proj.experts.1.weight', 'model.layers.17.mlp.down_proj.experts.2.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_up_proj.experts.0.weight', 'model.layers.17.mlp.gate_up_proj.experts.1.weight', 'model.layers.17.mlp.gate_up_proj.experts.2.weight', 'model.layers.17.mlp.gate_up_proj.gate.weight', 'model.layers.18.mlp.down_proj.experts.0.weight', 'model.layers.18.mlp.down_proj.experts.1.weight', 'model.layers.18.mlp.down_proj.experts.2.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_up_proj.experts.0.weight', 'model.layers.18.mlp.gate_up_proj.experts.1.weight', 'model.layers.18.mlp.gate_up_proj.experts.2.weight', 'model.layers.18.mlp.gate_up_proj.gate.weight', 'model.layers.19.mlp.down_proj.experts.0.weight', 'model.layers.19.mlp.down_proj.experts.1.weight', 'model.layers.19.mlp.down_proj.experts.2.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_up_proj.experts.0.weight', 'model.layers.19.mlp.gate_up_proj.experts.1.weight', 'model.layers.19.mlp.gate_up_proj.experts.2.weight', 'model.layers.19.mlp.gate_up_proj.gate.weight', 'model.layers.2.mlp.down_proj.experts.0.weight', 'model.layers.2.mlp.down_proj.experts.1.weight', 'model.layers.2.mlp.down_proj.experts.2.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_up_proj.experts.0.weight', 'model.layers.2.mlp.gate_up_proj.experts.1.weight', 'model.layers.2.mlp.gate_up_proj.experts.2.weight', 'model.layers.2.mlp.gate_up_proj.gate.weight', 'model.layers.20.mlp.down_proj.experts.0.weight', 'model.layers.20.mlp.down_proj.experts.1.weight', 'model.layers.20.mlp.down_proj.experts.2.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_up_proj.experts.0.weight', 'model.layers.20.mlp.gate_up_proj.experts.1.weight', 'model.layers.20.mlp.gate_up_proj.experts.2.weight', 'model.layers.20.mlp.gate_up_proj.gate.weight', 'model.layers.21.mlp.down_proj.experts.0.weight', 'model.layers.21.mlp.down_proj.experts.1.weight', 'model.layers.21.mlp.down_proj.experts.2.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_up_proj.experts.0.weight', 'model.layers.21.mlp.gate_up_proj.experts.1.weight', 'model.layers.21.mlp.gate_up_proj.experts.2.weight', 'model.layers.21.mlp.gate_up_proj.gate.weight', 'model.layers.22.mlp.down_proj.experts.0.weight', 'model.layers.22.mlp.down_proj.experts.1.weight', 'model.layers.22.mlp.down_proj.experts.2.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_up_proj.experts.0.weight', 'model.layers.22.mlp.gate_up_proj.experts.1.weight', 'model.layers.22.mlp.gate_up_proj.experts.2.weight', 'model.layers.22.mlp.gate_up_proj.gate.weight', 'model.layers.23.mlp.down_proj.experts.0.weight', 'model.layers.23.mlp.down_proj.experts.1.weight', 'model.layers.23.mlp.down_proj.experts.2.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_up_proj.experts.0.weight', 'model.layers.23.mlp.gate_up_proj.experts.1.weight', 'model.layers.23.mlp.gate_up_proj.experts.2.weight', 'model.layers.23.mlp.gate_up_proj.gate.weight', 'model.layers.24.mlp.down_proj.experts.0.weight', 'model.layers.24.mlp.down_proj.experts.1.weight', 'model.layers.24.mlp.down_proj.experts.2.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_up_proj.experts.0.weight', 'model.layers.24.mlp.gate_up_proj.experts.1.weight', 'model.layers.24.mlp.gate_up_proj.experts.2.weight', 'model.layers.24.mlp.gate_up_proj.gate.weight', 'model.layers.25.mlp.down_proj.experts.0.weight', 'model.layers.25.mlp.down_proj.experts.1.weight', 'model.layers.25.mlp.down_proj.experts.2.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_up_proj.experts.0.weight', 'model.layers.25.mlp.gate_up_proj.experts.1.weight', 'model.layers.25.mlp.gate_up_proj.experts.2.weight', 'model.layers.25.mlp.gate_up_proj.gate.weight', 'model.layers.26.mlp.down_proj.experts.0.weight', 'model.layers.26.mlp.down_proj.experts.1.weight', 'model.layers.26.mlp.down_proj.experts.2.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_up_proj.experts.0.weight', 'model.layers.26.mlp.gate_up_proj.experts.1.weight', 'model.layers.26.mlp.gate_up_proj.experts.2.weight', 'model.layers.26.mlp.gate_up_proj.gate.weight', 'model.layers.27.mlp.down_proj.experts.0.weight', 'model.layers.27.mlp.down_proj.experts.1.weight', 'model.layers.27.mlp.down_proj.experts.2.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_up_proj.experts.0.weight', 'model.layers.27.mlp.gate_up_proj.experts.1.weight', 'model.layers.27.mlp.gate_up_proj.experts.2.weight', 'model.layers.27.mlp.gate_up_proj.gate.weight', 'model.layers.28.mlp.down_proj.experts.0.weight', 'model.layers.28.mlp.down_proj.experts.1.weight', 'model.layers.28.mlp.down_proj.experts.2.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_up_proj.experts.0.weight', 'model.layers.28.mlp.gate_up_proj.experts.1.weight', 'model.layers.28.mlp.gate_up_proj.experts.2.weight', 'model.layers.28.mlp.gate_up_proj.gate.weight', 'model.layers.29.mlp.down_proj.experts.0.weight', 'model.layers.29.mlp.down_proj.experts.1.weight', 'model.layers.29.mlp.down_proj.experts.2.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_up_proj.experts.0.weight', 'model.layers.29.mlp.gate_up_proj.experts.1.weight', 'model.layers.29.mlp.gate_up_proj.experts.2.weight', 'model.layers.29.mlp.gate_up_proj.gate.weight', 'model.layers.3.mlp.down_proj.experts.0.weight', 'model.layers.3.mlp.down_proj.experts.1.weight', 'model.layers.3.mlp.down_proj.experts.2.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_up_proj.experts.0.weight', 'model.layers.3.mlp.gate_up_proj.experts.1.weight', 'model.layers.3.mlp.gate_up_proj.experts.2.weight', 'model.layers.3.mlp.gate_up_proj.gate.weight', 'model.layers.30.mlp.down_proj.experts.0.weight', 'model.layers.30.mlp.down_proj.experts.1.weight', 'model.layers.30.mlp.down_proj.experts.2.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_up_proj.experts.0.weight', 'model.layers.30.mlp.gate_up_proj.experts.1.weight', 'model.layers.30.mlp.gate_up_proj.experts.2.weight', 'model.layers.30.mlp.gate_up_proj.gate.weight', 'model.layers.31.mlp.down_proj.experts.0.weight', 'model.layers.31.mlp.down_proj.experts.1.weight', 'model.layers.31.mlp.down_proj.experts.2.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_up_proj.experts.0.weight', 'model.layers.31.mlp.gate_up_proj.experts.1.weight', 'model.layers.31.mlp.gate_up_proj.experts.2.weight', 'model.layers.31.mlp.gate_up_proj.gate.weight', 'model.layers.4.mlp.down_proj.experts.0.weight', 'model.layers.4.mlp.down_proj.experts.1.weight', 'model.layers.4.mlp.down_proj.experts.2.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_up_proj.experts.0.weight', 'model.layers.4.mlp.gate_up_proj.experts.1.weight', 'model.layers.4.mlp.gate_up_proj.experts.2.weight', 'model.layers.4.mlp.gate_up_proj.gate.weight', 'model.layers.5.mlp.down_proj.experts.0.weight', 'model.layers.5.mlp.down_proj.experts.1.weight', 'model.layers.5.mlp.down_proj.experts.2.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_up_proj.experts.0.weight', 'model.layers.5.mlp.gate_up_proj.experts.1.weight', 'model.layers.5.mlp.gate_up_proj.experts.2.weight', 'model.layers.5.mlp.gate_up_proj.gate.weight', 'model.layers.6.mlp.down_proj.experts.0.weight', 'model.layers.6.mlp.down_proj.experts.1.weight', 'model.layers.6.mlp.down_proj.experts.2.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_up_proj.experts.0.weight', 'model.layers.6.mlp.gate_up_proj.experts.1.weight', 'model.layers.6.mlp.gate_up_proj.experts.2.weight', 'model.layers.6.mlp.gate_up_proj.gate.weight', 'model.layers.7.mlp.down_proj.experts.0.weight', 'model.layers.7.mlp.down_proj.experts.1.weight', 'model.layers.7.mlp.down_proj.experts.2.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_up_proj.experts.0.weight', 'model.layers.7.mlp.gate_up_proj.experts.1.weight', 'model.layers.7.mlp.gate_up_proj.experts.2.weight', 'model.layers.7.mlp.gate_up_proj.gate.weight', 'model.layers.8.mlp.down_proj.experts.0.weight', 'model.layers.8.mlp.down_proj.experts.1.weight', 'model.layers.8.mlp.down_proj.experts.2.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_up_proj.experts.0.weight', 'model.layers.8.mlp.gate_up_proj.experts.1.weight', 'model.layers.8.mlp.gate_up_proj.experts.2.weight', 'model.layers.8.mlp.gate_up_proj.gate.weight', 'model.layers.9.mlp.down_proj.experts.0.weight', 'model.layers.9.mlp.down_proj.experts.1.weight', 'model.layers.9.mlp.down_proj.experts.2.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_up_proj.experts.0.weight', 'model.layers.9.mlp.gate_up_proj.experts.1.weight', 'model.layers.9.mlp.gate_up_proj.experts.2.weight', 'model.layers.9.mlp.gate_up_proj.gate.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["# load the composed checkkpoint\n", "import torch\n", "from mergoo.models.modeling_phi3 import Phi3ForCausalLM\n", "\n", "model = Phi3ForCausalLM.from_pretrained(\n", "    model_id, \n", "    device_map=\"auto\", \n", "    torch_dtype=torch.bfloat16,\n", ")# 'gate' / router layers are untrained hence loaded warning would appear for them"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(387, 227)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# train only router (gating) layers\n", "n_weights, n_router_weights  = 0,0\n", "for name, weight in model.named_parameters():\n", "    if \"gate\" not in name:\n", "        weight.requires_grad_(False)\n", "        n_router_weights += 1\n", "    n_weights += 1\n", "n_weights, n_router_weights"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import datasets\n", "import random\n", "\n", "dataset = datasets.load_dataset(\"iamtarun/python_code_instructions_18k_alpaca\")['train']\n", "dataset = dataset['prompt']\n", "random.shuffle(dataset)\n", "dataset_train =  datasets.Dataset.from_dict(dict(prompt=dataset[:-1000]))\n", "dataset_test = datasets.Dataset.from_dict(dict(prompt=dataset[-1000:]))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Dataset({\n", "     features: ['prompt'],\n", "     num_rows: 17612\n", " }),\n", " Dataset({\n", "     features: ['prompt'],\n", "     num_rows: 1000\n", " }))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset_train, dataset_test"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "/home/<USER>/miniconda3/envs/phi3/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:246: UserWarning: You didn't pass a `max_seq_length` argument to the SFTTrainer, this will default to 1024\n", "  warnings.warn(\n", "Map:   0%|          | 0/17612 [00:00<?, ? examples/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Map: 100%|██████████| 17612/17612 [00:01<00:00, 9167.40 examples/s] \n", "Map: 100%|██████████| 1000/1000 [00:00<00:00, 12832.94 examples/s]\n", "/home/<USER>/miniconda3/envs/phi3/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:318: UserWarning: You passed a tokenizer with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `tokenizer.padding_side = 'right'` to your code.\n", "  warnings.warn(\n"]}], "source": ["from trl import SFTTrainer\n", "from transformers import TrainingArguments\n", "\n", "trainer_args = TrainingArguments(\n", "    output_dir= \"checkpoints/phi3_moe\",\n", "    per_device_train_batch_size = 1,\n", "    per_device_eval_batch_size = 1, \n", "    learning_rate= 1e-5,\n", "    save_total_limit=1,\n", "    num_train_epochs=1,\n", "    eval_steps= 5000,\n", "    logging_strategy=\"steps\",\n", "    logging_steps= 25,\n", "    gradient_accumulation_steps=4,\n", "    bf16=True\n", ")\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model,\n", "    args= trainer_args,\n", "    train_dataset= dataset_train,\n", "    eval_dataset= dataset_test,\n", "    dataset_text_field=\"prompt\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}