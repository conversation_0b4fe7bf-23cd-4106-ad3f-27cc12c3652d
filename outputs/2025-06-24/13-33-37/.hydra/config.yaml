experiment_name: mistral-customer-support-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - customer-support
  notes: Training Mistral MoE model for customer support with router-only training
deepspeed:
  enabled: true
  config_path: conf/deepspeed_config.json
model:
  model_type: mistral
  num_experts_per_tok: 2
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: predibase/customer_support
  - expert_name: adapter_2
    model_id: predibase/customer_support_accounts
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1.0e-05
  save_total_limit: 1
  num_train_epochs: 1
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: bitext/Bitext-customer-support-llm-chatbot-training-dataset
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe
  output_dir: data/mistral_cs_lora_moe
