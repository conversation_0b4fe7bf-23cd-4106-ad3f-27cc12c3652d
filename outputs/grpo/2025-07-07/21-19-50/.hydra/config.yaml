experiment_name: mistral-economic-moe-grpo
seed: 42
wandb:
  enabled: true
  project: mistral-moe-grpo-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  - grpo
  notes: Training Mistral MoE model for economic with GRPO and DeepSeek-style reward
    function
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
grpo:
  enabled: true
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 5.0e-06
  num_train_epochs: 3
  num_generations: 4
  max_completion_length: 256
  max_prompt_length: 512
  temperature: 0.7
  beta: 0.0
llama_guard:
  enabled: true
  model_id: meta-llama/Llama-Guard-3-8B
  device_map: auto
  torch_dtype: bfloat16
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1_grpo
  output_dir: data/output/mistral_lora_moe_economic_top1_grpo
