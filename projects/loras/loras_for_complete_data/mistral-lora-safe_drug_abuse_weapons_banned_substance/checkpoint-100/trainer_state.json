{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.45977011494252873, "eval_steps": 500, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 3.2508132457733154, "learning_rate": 0.00019976958525345624, "loss": 1.3891, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.541885256767273, "learning_rate": 0.00019769585253456222, "loss": 1.1326, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.1821074485778809, "learning_rate": 0.00019539170506912442, "loss": 1.0186, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.2840911149978638, "learning_rate": 0.00019308755760368663, "loss": 0.9854, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.2454668283462524, "learning_rate": 0.00019078341013824886, "loss": 0.9527, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.1894640922546387, "learning_rate": 0.00018847926267281107, "loss": 0.9555, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.2689568996429443, "learning_rate": 0.00018617511520737328, "loss": 0.9277, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.2895137071609497, "learning_rate": 0.00018387096774193548, "loss": 0.9049, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.2527351379394531, "learning_rate": 0.0001815668202764977, "loss": 0.8923, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.2242168188095093, "learning_rate": 0.0001792626728110599, "loss": 0.8884, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.2326970100402832, "learning_rate": 0.00017695852534562213, "loss": 0.8759, "step": 100}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 7.0223320645632e+16, "train_batch_size": 16, "trial_name": null, "trial_params": null}