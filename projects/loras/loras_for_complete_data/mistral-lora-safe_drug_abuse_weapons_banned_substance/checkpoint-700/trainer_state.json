{"best_metric": null, "best_model_checkpoint": null, "epoch": 3.218390804597701, "eval_steps": 500, "global_step": 700, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 3.2508132457733154, "learning_rate": 0.00019976958525345624, "loss": 1.3891, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.541885256767273, "learning_rate": 0.00019769585253456222, "loss": 1.1326, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.1821074485778809, "learning_rate": 0.00019539170506912442, "loss": 1.0186, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.2840911149978638, "learning_rate": 0.00019308755760368663, "loss": 0.9854, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.2454668283462524, "learning_rate": 0.00019078341013824886, "loss": 0.9527, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.1894640922546387, "learning_rate": 0.00018847926267281107, "loss": 0.9555, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.2689568996429443, "learning_rate": 0.00018617511520737328, "loss": 0.9277, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.2895137071609497, "learning_rate": 0.00018387096774193548, "loss": 0.9049, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.2527351379394531, "learning_rate": 0.0001815668202764977, "loss": 0.8923, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.2242168188095093, "learning_rate": 0.0001792626728110599, "loss": 0.8884, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.2326970100402832, "learning_rate": 0.00017695852534562213, "loss": 0.8759, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.267621397972107, "learning_rate": 0.00017465437788018436, "loss": 0.8767, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.2533502578735352, "learning_rate": 0.00017235023041474657, "loss": 0.8688, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.4397333860397339, "learning_rate": 0.00017004608294930878, "loss": 0.8528, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.226040005683899, "learning_rate": 0.00016774193548387098, "loss": 0.8432, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.2485771179199219, "learning_rate": 0.0001654377880184332, "loss": 0.8402, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 1.212713360786438, "learning_rate": 0.0001631336405529954, "loss": 0.8574, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.1578097343444824, "learning_rate": 0.0001608294930875576, "loss": 0.8521, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 1.31389319896698, "learning_rate": 0.00015852534562211984, "loss": 0.8459, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 1.1119751930236816, "learning_rate": 0.00015622119815668204, "loss": 0.8153, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 1.1800655126571655, "learning_rate": 0.00015391705069124425, "loss": 0.8415, "step": 200}, {"epoch": 0.9655172413793104, "grad_norm": 1.365053653717041, "learning_rate": 0.00015161290322580646, "loss": 0.842, "step": 210}, {"epoch": 1.0114942528735633, "grad_norm": 1.1592739820480347, "learning_rate": 0.00014930875576036866, "loss": 0.7798, "step": 220}, {"epoch": 1.0574712643678161, "grad_norm": 1.275284767150879, "learning_rate": 0.00014700460829493087, "loss": 0.6578, "step": 230}, {"epoch": 1.103448275862069, "grad_norm": 1.2712820768356323, "learning_rate": 0.0001447004608294931, "loss": 0.6495, "step": 240}, {"epoch": 1.1494252873563218, "grad_norm": 1.331921935081482, "learning_rate": 0.0001423963133640553, "loss": 0.6387, "step": 250}, {"epoch": 1.1954022988505748, "grad_norm": 1.2873446941375732, "learning_rate": 0.00014009216589861752, "loss": 0.6554, "step": 260}, {"epoch": 1.2413793103448276, "grad_norm": 1.2917057275772095, "learning_rate": 0.00013778801843317972, "loss": 0.6506, "step": 270}, {"epoch": 1.2873563218390804, "grad_norm": 1.224185585975647, "learning_rate": 0.00013548387096774193, "loss": 0.6345, "step": 280}, {"epoch": 1.3333333333333333, "grad_norm": 1.2430812120437622, "learning_rate": 0.00013317972350230414, "loss": 0.6463, "step": 290}, {"epoch": 1.3793103448275863, "grad_norm": 1.268716812133789, "learning_rate": 0.00013087557603686637, "loss": 0.6433, "step": 300}, {"epoch": 1.4252873563218391, "grad_norm": 1.2812669277191162, "learning_rate": 0.00012857142857142858, "loss": 0.6609, "step": 310}, {"epoch": 1.471264367816092, "grad_norm": 1.3386069536209106, "learning_rate": 0.0001262672811059908, "loss": 0.6749, "step": 320}, {"epoch": 1.5172413793103448, "grad_norm": 1.5115489959716797, "learning_rate": 0.00012396313364055302, "loss": 0.6548, "step": 330}, {"epoch": 1.5632183908045976, "grad_norm": 1.2180769443511963, "learning_rate": 0.00012165898617511522, "loss": 0.6462, "step": 340}, {"epoch": 1.6091954022988506, "grad_norm": 1.2334893941879272, "learning_rate": 0.00011935483870967743, "loss": 0.6531, "step": 350}, {"epoch": 1.6551724137931034, "grad_norm": 1.4911540746688843, "learning_rate": 0.00011705069124423964, "loss": 0.6688, "step": 360}, {"epoch": 1.7011494252873565, "grad_norm": 1.3182061910629272, "learning_rate": 0.00011474654377880186, "loss": 0.653, "step": 370}, {"epoch": 1.7471264367816093, "grad_norm": 1.2513647079467773, "learning_rate": 0.00011244239631336406, "loss": 0.6504, "step": 380}, {"epoch": 1.793103448275862, "grad_norm": 1.2841428518295288, "learning_rate": 0.00011013824884792627, "loss": 0.6751, "step": 390}, {"epoch": 1.839080459770115, "grad_norm": 1.489405632019043, "learning_rate": 0.00010783410138248849, "loss": 0.6458, "step": 400}, {"epoch": 1.8850574712643677, "grad_norm": 1.265812635421753, "learning_rate": 0.0001055299539170507, "loss": 0.6546, "step": 410}, {"epoch": 1.9310344827586206, "grad_norm": 1.2787177562713623, "learning_rate": 0.0001032258064516129, "loss": 0.6417, "step": 420}, {"epoch": 1.9770114942528736, "grad_norm": 1.2819645404815674, "learning_rate": 0.00010092165898617512, "loss": 0.6319, "step": 430}, {"epoch": 2.0229885057471266, "grad_norm": 1.441773772239685, "learning_rate": 9.861751152073733e-05, "loss": 0.5747, "step": 440}, {"epoch": 2.0689655172413794, "grad_norm": 1.5544307231903076, "learning_rate": 9.631336405529955e-05, "loss": 0.4708, "step": 450}, {"epoch": 2.1149425287356323, "grad_norm": 1.5452107191085815, "learning_rate": 9.400921658986176e-05, "loss": 0.4713, "step": 460}, {"epoch": 2.160919540229885, "grad_norm": 1.6736493110656738, "learning_rate": 9.170506912442398e-05, "loss": 0.47, "step": 470}, {"epoch": 2.206896551724138, "grad_norm": 1.6922945976257324, "learning_rate": 8.940092165898618e-05, "loss": 0.4595, "step": 480}, {"epoch": 2.2528735632183907, "grad_norm": 1.4982961416244507, "learning_rate": 8.709677419354839e-05, "loss": 0.4723, "step": 490}, {"epoch": 2.2988505747126435, "grad_norm": 1.5107800960540771, "learning_rate": 8.479262672811061e-05, "loss": 0.4571, "step": 500}, {"epoch": 2.344827586206897, "grad_norm": 1.6183106899261475, "learning_rate": 8.248847926267282e-05, "loss": 0.4708, "step": 510}, {"epoch": 2.3908045977011496, "grad_norm": 1.5767529010772705, "learning_rate": 8.018433179723502e-05, "loss": 0.4676, "step": 520}, {"epoch": 2.4367816091954024, "grad_norm": 1.6287956237792969, "learning_rate": 7.788018433179723e-05, "loss": 0.4813, "step": 530}, {"epoch": 2.4827586206896552, "grad_norm": 1.6484687328338623, "learning_rate": 7.557603686635945e-05, "loss": 0.4796, "step": 540}, {"epoch": 2.528735632183908, "grad_norm": 1.5044227838516235, "learning_rate": 7.327188940092167e-05, "loss": 0.4768, "step": 550}, {"epoch": 2.574712643678161, "grad_norm": 1.6746176481246948, "learning_rate": 7.096774193548388e-05, "loss": 0.4752, "step": 560}, {"epoch": 2.6206896551724137, "grad_norm": 1.693833589553833, "learning_rate": 6.86635944700461e-05, "loss": 0.4817, "step": 570}, {"epoch": 2.6666666666666665, "grad_norm": 1.6240651607513428, "learning_rate": 6.63594470046083e-05, "loss": 0.4805, "step": 580}, {"epoch": 2.7126436781609193, "grad_norm": 1.505884051322937, "learning_rate": 6.405529953917051e-05, "loss": 0.4844, "step": 590}, {"epoch": 2.7586206896551726, "grad_norm": 1.614022135734558, "learning_rate": 6.175115207373272e-05, "loss": 0.4752, "step": 600}, {"epoch": 2.8045977011494254, "grad_norm": 1.676604986190796, "learning_rate": 5.944700460829493e-05, "loss": 0.4786, "step": 610}, {"epoch": 2.8505747126436782, "grad_norm": 1.4822946786880493, "learning_rate": 5.714285714285714e-05, "loss": 0.4827, "step": 620}, {"epoch": 2.896551724137931, "grad_norm": 1.6984790563583374, "learning_rate": 5.4838709677419355e-05, "loss": 0.4819, "step": 630}, {"epoch": 2.942528735632184, "grad_norm": 1.588157057762146, "learning_rate": 5.253456221198156e-05, "loss": 0.4706, "step": 640}, {"epoch": 2.9885057471264367, "grad_norm": 1.797584891319275, "learning_rate": 5.023041474654379e-05, "loss": 0.474, "step": 650}, {"epoch": 3.0344827586206895, "grad_norm": 2.472745180130005, "learning_rate": 4.792626728110599e-05, "loss": 0.3581, "step": 660}, {"epoch": 3.0804597701149423, "grad_norm": 1.697892665863037, "learning_rate": 4.562211981566821e-05, "loss": 0.3313, "step": 670}, {"epoch": 3.1264367816091956, "grad_norm": 1.6586815118789673, "learning_rate": 4.3317972350230415e-05, "loss": 0.3263, "step": 680}, {"epoch": 3.1724137931034484, "grad_norm": 1.761593222618103, "learning_rate": 4.101382488479263e-05, "loss": 0.3204, "step": 690}, {"epoch": 3.218390804597701, "grad_norm": 1.8144618272781372, "learning_rate": 3.870967741935484e-05, "loss": 0.3136, "step": 700}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4.913203596263424e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}