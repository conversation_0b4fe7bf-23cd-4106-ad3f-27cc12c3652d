{"best_metric": null, "best_model_checkpoint": null, "epoch": 1.3793103448275863, "eval_steps": 500, "global_step": 300, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 3.2508132457733154, "learning_rate": 0.00019976958525345624, "loss": 1.3891, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.541885256767273, "learning_rate": 0.00019769585253456222, "loss": 1.1326, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.1821074485778809, "learning_rate": 0.00019539170506912442, "loss": 1.0186, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.2840911149978638, "learning_rate": 0.00019308755760368663, "loss": 0.9854, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.2454668283462524, "learning_rate": 0.00019078341013824886, "loss": 0.9527, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.1894640922546387, "learning_rate": 0.00018847926267281107, "loss": 0.9555, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.2689568996429443, "learning_rate": 0.00018617511520737328, "loss": 0.9277, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.2895137071609497, "learning_rate": 0.00018387096774193548, "loss": 0.9049, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.2527351379394531, "learning_rate": 0.0001815668202764977, "loss": 0.8923, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.2242168188095093, "learning_rate": 0.0001792626728110599, "loss": 0.8884, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.2326970100402832, "learning_rate": 0.00017695852534562213, "loss": 0.8759, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.267621397972107, "learning_rate": 0.00017465437788018436, "loss": 0.8767, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.2533502578735352, "learning_rate": 0.00017235023041474657, "loss": 0.8688, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.4397333860397339, "learning_rate": 0.00017004608294930878, "loss": 0.8528, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.226040005683899, "learning_rate": 0.00016774193548387098, "loss": 0.8432, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.2485771179199219, "learning_rate": 0.0001654377880184332, "loss": 0.8402, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 1.212713360786438, "learning_rate": 0.0001631336405529954, "loss": 0.8574, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.1578097343444824, "learning_rate": 0.0001608294930875576, "loss": 0.8521, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 1.31389319896698, "learning_rate": 0.00015852534562211984, "loss": 0.8459, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 1.1119751930236816, "learning_rate": 0.00015622119815668204, "loss": 0.8153, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 1.1800655126571655, "learning_rate": 0.00015391705069124425, "loss": 0.8415, "step": 200}, {"epoch": 0.9655172413793104, "grad_norm": 1.365053653717041, "learning_rate": 0.00015161290322580646, "loss": 0.842, "step": 210}, {"epoch": 1.0114942528735633, "grad_norm": 1.1592739820480347, "learning_rate": 0.00014930875576036866, "loss": 0.7798, "step": 220}, {"epoch": 1.0574712643678161, "grad_norm": 1.275284767150879, "learning_rate": 0.00014700460829493087, "loss": 0.6578, "step": 230}, {"epoch": 1.103448275862069, "grad_norm": 1.2712820768356323, "learning_rate": 0.0001447004608294931, "loss": 0.6495, "step": 240}, {"epoch": 1.1494252873563218, "grad_norm": 1.331921935081482, "learning_rate": 0.0001423963133640553, "loss": 0.6387, "step": 250}, {"epoch": 1.1954022988505748, "grad_norm": 1.2873446941375732, "learning_rate": 0.00014009216589861752, "loss": 0.6554, "step": 260}, {"epoch": 1.2413793103448276, "grad_norm": 1.2917057275772095, "learning_rate": 0.00013778801843317972, "loss": 0.6506, "step": 270}, {"epoch": 1.2873563218390804, "grad_norm": 1.224185585975647, "learning_rate": 0.00013548387096774193, "loss": 0.6345, "step": 280}, {"epoch": 1.3333333333333333, "grad_norm": 1.2430812120437622, "learning_rate": 0.00013317972350230414, "loss": 0.6463, "step": 290}, {"epoch": 1.3793103448275863, "grad_norm": 1.268716812133789, "learning_rate": 0.00013087557603686637, "loss": 0.6433, "step": 300}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2.105137725014016e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}