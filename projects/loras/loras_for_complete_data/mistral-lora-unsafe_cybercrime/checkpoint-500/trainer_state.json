{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.48851978505129456, "eval_steps": 500, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0009770395701025891, "grad_norm": 4.318492889404297, "learning_rate": 0.00019995112414467254, "loss": 2.1321, "step": 1}, {"epoch": 0.009770395701025891, "grad_norm": 1.6686569452285767, "learning_rate": 0.00019951124144672533, "loss": 1.6575, "step": 10}, {"epoch": 0.019540791402051783, "grad_norm": 1.4235731363296509, "learning_rate": 0.00019902248289345066, "loss": 1.5519, "step": 20}, {"epoch": 0.029311187103077674, "grad_norm": 1.3094052076339722, "learning_rate": 0.00019853372434017595, "loss": 1.5619, "step": 30}, {"epoch": 0.039081582804103565, "grad_norm": 1.4127061367034912, "learning_rate": 0.00019804496578690127, "loss": 1.5646, "step": 40}, {"epoch": 0.048851978505129456, "grad_norm": 1.3963359594345093, "learning_rate": 0.0001975562072336266, "loss": 1.464, "step": 50}, {"epoch": 0.05862237420615535, "grad_norm": 1.407443642616272, "learning_rate": 0.00019706744868035192, "loss": 1.5314, "step": 60}, {"epoch": 0.06839276990718124, "grad_norm": 1.4019520282745361, "learning_rate": 0.00019657869012707724, "loss": 1.5258, "step": 70}, {"epoch": 0.07816316560820713, "grad_norm": 1.3518329858779907, "learning_rate": 0.00019608993157380257, "loss": 1.5087, "step": 80}, {"epoch": 0.08793356130923302, "grad_norm": 1.3117033243179321, "learning_rate": 0.00019560117302052786, "loss": 1.5266, "step": 90}, {"epoch": 0.09770395701025891, "grad_norm": 1.4562606811523438, "learning_rate": 0.0001951124144672532, "loss": 1.5173, "step": 100}, {"epoch": 0.1074743527112848, "grad_norm": 1.4015308618545532, "learning_rate": 0.0001946236559139785, "loss": 1.5018, "step": 110}, {"epoch": 0.1172447484123107, "grad_norm": 1.3310906887054443, "learning_rate": 0.00019413489736070383, "loss": 1.5091, "step": 120}, {"epoch": 0.1270151441133366, "grad_norm": 1.410440444946289, "learning_rate": 0.00019364613880742916, "loss": 1.4809, "step": 130}, {"epoch": 0.13678553981436248, "grad_norm": 1.4034721851348877, "learning_rate": 0.00019315738025415445, "loss": 1.4878, "step": 140}, {"epoch": 0.14655593551538837, "grad_norm": 1.327186107635498, "learning_rate": 0.00019266862170087977, "loss": 1.4901, "step": 150}, {"epoch": 0.15632633121641426, "grad_norm": 1.4575003385543823, "learning_rate": 0.0001921798631476051, "loss": 1.4863, "step": 160}, {"epoch": 0.16609672691744015, "grad_norm": 1.3933295011520386, "learning_rate": 0.00019169110459433042, "loss": 1.4822, "step": 170}, {"epoch": 0.17586712261846604, "grad_norm": 1.3313087224960327, "learning_rate": 0.00019120234604105574, "loss": 1.4688, "step": 180}, {"epoch": 0.18563751831949193, "grad_norm": 1.3334952592849731, "learning_rate": 0.00019071358748778104, "loss": 1.5273, "step": 190}, {"epoch": 0.19540791402051783, "grad_norm": 1.34340238571167, "learning_rate": 0.00019022482893450636, "loss": 1.5063, "step": 200}, {"epoch": 0.20517830972154372, "grad_norm": 1.3555039167404175, "learning_rate": 0.0001897360703812317, "loss": 1.4749, "step": 210}, {"epoch": 0.2149487054225696, "grad_norm": 1.3215388059616089, "learning_rate": 0.000189247311827957, "loss": 1.4737, "step": 220}, {"epoch": 0.2247191011235955, "grad_norm": 1.4563490152359009, "learning_rate": 0.00018875855327468233, "loss": 1.4457, "step": 230}, {"epoch": 0.2344894968246214, "grad_norm": 1.4188816547393799, "learning_rate": 0.00018826979472140766, "loss": 1.472, "step": 240}, {"epoch": 0.24425989252564728, "grad_norm": 1.3074414730072021, "learning_rate": 0.00018778103616813295, "loss": 1.4956, "step": 250}, {"epoch": 0.2540302882266732, "grad_norm": 1.3118152618408203, "learning_rate": 0.00018729227761485825, "loss": 1.4874, "step": 260}, {"epoch": 0.26380068392769906, "grad_norm": 1.6247066259384155, "learning_rate": 0.00018680351906158357, "loss": 1.4447, "step": 270}, {"epoch": 0.27357107962872496, "grad_norm": 1.4265419244766235, "learning_rate": 0.0001863147605083089, "loss": 1.4806, "step": 280}, {"epoch": 0.28334147532975085, "grad_norm": 1.3725743293762207, "learning_rate": 0.00018582600195503422, "loss": 1.4508, "step": 290}, {"epoch": 0.29311187103077674, "grad_norm": 1.447954535484314, "learning_rate": 0.00018533724340175954, "loss": 1.4533, "step": 300}, {"epoch": 0.30288226673180263, "grad_norm": 1.4657621383666992, "learning_rate": 0.00018484848484848484, "loss": 1.4718, "step": 310}, {"epoch": 0.3126526624328285, "grad_norm": 1.4806748628616333, "learning_rate": 0.00018435972629521016, "loss": 1.4588, "step": 320}, {"epoch": 0.3224230581338544, "grad_norm": 1.3653591871261597, "learning_rate": 0.00018387096774193548, "loss": 1.4565, "step": 330}, {"epoch": 0.3321934538348803, "grad_norm": 1.3973519802093506, "learning_rate": 0.0001833822091886608, "loss": 1.4309, "step": 340}, {"epoch": 0.3419638495359062, "grad_norm": 1.4750550985336304, "learning_rate": 0.00018289345063538613, "loss": 1.442, "step": 350}, {"epoch": 0.3517342452369321, "grad_norm": 1.4277312755584717, "learning_rate": 0.00018240469208211145, "loss": 1.4692, "step": 360}, {"epoch": 0.361504640937958, "grad_norm": 1.4753135442733765, "learning_rate": 0.00018191593352883675, "loss": 1.4524, "step": 370}, {"epoch": 0.37127503663898387, "grad_norm": 1.3918225765228271, "learning_rate": 0.00018142717497556207, "loss": 1.4105, "step": 380}, {"epoch": 0.38104543234000976, "grad_norm": 1.5128066539764404, "learning_rate": 0.0001809384164222874, "loss": 1.4524, "step": 390}, {"epoch": 0.39081582804103565, "grad_norm": 1.3937450647354126, "learning_rate": 0.00018044965786901272, "loss": 1.4726, "step": 400}, {"epoch": 0.40058622374206154, "grad_norm": 1.3960708379745483, "learning_rate": 0.00017996089931573804, "loss": 1.4841, "step": 410}, {"epoch": 0.41035661944308743, "grad_norm": 1.4202630519866943, "learning_rate": 0.00017947214076246334, "loss": 1.4603, "step": 420}, {"epoch": 0.4201270151441133, "grad_norm": 1.4798625707626343, "learning_rate": 0.00017898338220918866, "loss": 1.4141, "step": 430}, {"epoch": 0.4298974108451392, "grad_norm": 1.535984992980957, "learning_rate": 0.00017849462365591398, "loss": 1.4592, "step": 440}, {"epoch": 0.4396678065461651, "grad_norm": 1.6673039197921753, "learning_rate": 0.0001780058651026393, "loss": 1.4576, "step": 450}, {"epoch": 0.449438202247191, "grad_norm": 1.4512465000152588, "learning_rate": 0.00017751710654936463, "loss": 1.4643, "step": 460}, {"epoch": 0.4592085979482169, "grad_norm": 1.5436588525772095, "learning_rate": 0.00017702834799608992, "loss": 1.4503, "step": 470}, {"epoch": 0.4689789936492428, "grad_norm": 1.4285378456115723, "learning_rate": 0.00017653958944281525, "loss": 1.4719, "step": 480}, {"epoch": 0.4787493893502687, "grad_norm": 1.4874051809310913, "learning_rate": 0.00017605083088954057, "loss": 1.4417, "step": 490}, {"epoch": 0.48851978505129456, "grad_norm": 1.6446212530136108, "learning_rate": 0.0001755620723362659, "loss": 1.4403, "step": 500}], "logging_steps": 10, "max_steps": 4092, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.970367572017152e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}