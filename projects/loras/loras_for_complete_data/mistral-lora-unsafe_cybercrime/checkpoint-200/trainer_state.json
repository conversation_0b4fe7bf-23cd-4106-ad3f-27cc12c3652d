{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.19540791402051783, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0009770395701025891, "grad_norm": 4.318492889404297, "learning_rate": 0.00019995112414467254, "loss": 2.1321, "step": 1}, {"epoch": 0.009770395701025891, "grad_norm": 1.6686569452285767, "learning_rate": 0.00019951124144672533, "loss": 1.6575, "step": 10}, {"epoch": 0.019540791402051783, "grad_norm": 1.4235731363296509, "learning_rate": 0.00019902248289345066, "loss": 1.5519, "step": 20}, {"epoch": 0.029311187103077674, "grad_norm": 1.3094052076339722, "learning_rate": 0.00019853372434017595, "loss": 1.5619, "step": 30}, {"epoch": 0.039081582804103565, "grad_norm": 1.4127061367034912, "learning_rate": 0.00019804496578690127, "loss": 1.5646, "step": 40}, {"epoch": 0.048851978505129456, "grad_norm": 1.3963359594345093, "learning_rate": 0.0001975562072336266, "loss": 1.464, "step": 50}, {"epoch": 0.05862237420615535, "grad_norm": 1.407443642616272, "learning_rate": 0.00019706744868035192, "loss": 1.5314, "step": 60}, {"epoch": 0.06839276990718124, "grad_norm": 1.4019520282745361, "learning_rate": 0.00019657869012707724, "loss": 1.5258, "step": 70}, {"epoch": 0.07816316560820713, "grad_norm": 1.3518329858779907, "learning_rate": 0.00019608993157380257, "loss": 1.5087, "step": 80}, {"epoch": 0.08793356130923302, "grad_norm": 1.3117033243179321, "learning_rate": 0.00019560117302052786, "loss": 1.5266, "step": 90}, {"epoch": 0.09770395701025891, "grad_norm": 1.4562606811523438, "learning_rate": 0.0001951124144672532, "loss": 1.5173, "step": 100}, {"epoch": 0.1074743527112848, "grad_norm": 1.4015308618545532, "learning_rate": 0.0001946236559139785, "loss": 1.5018, "step": 110}, {"epoch": 0.1172447484123107, "grad_norm": 1.3310906887054443, "learning_rate": 0.00019413489736070383, "loss": 1.5091, "step": 120}, {"epoch": 0.1270151441133366, "grad_norm": 1.410440444946289, "learning_rate": 0.00019364613880742916, "loss": 1.4809, "step": 130}, {"epoch": 0.13678553981436248, "grad_norm": 1.4034721851348877, "learning_rate": 0.00019315738025415445, "loss": 1.4878, "step": 140}, {"epoch": 0.14655593551538837, "grad_norm": 1.327186107635498, "learning_rate": 0.00019266862170087977, "loss": 1.4901, "step": 150}, {"epoch": 0.15632633121641426, "grad_norm": 1.4575003385543823, "learning_rate": 0.0001921798631476051, "loss": 1.4863, "step": 160}, {"epoch": 0.16609672691744015, "grad_norm": 1.3933295011520386, "learning_rate": 0.00019169110459433042, "loss": 1.4822, "step": 170}, {"epoch": 0.17586712261846604, "grad_norm": 1.3313087224960327, "learning_rate": 0.00019120234604105574, "loss": 1.4688, "step": 180}, {"epoch": 0.18563751831949193, "grad_norm": 1.3334952592849731, "learning_rate": 0.00019071358748778104, "loss": 1.5273, "step": 190}, {"epoch": 0.19540791402051783, "grad_norm": 1.34340238571167, "learning_rate": 0.00019022482893450636, "loss": 1.5063, "step": 200}], "logging_steps": 10, "max_steps": 4092, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 7.8583239770112e+16, "train_batch_size": 16, "trial_name": null, "trial_params": null}