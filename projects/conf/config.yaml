# Experiment configuration
experiment_name: "mistral-economic-moe"
seed: 42

# Weights & Biases configuration
wandb:
  enabled: true
  project: "mistral-moe-training"
  run_name: null  # Will use experiment_name if null
  tags: ["mistral", "moe", "economic"]
  notes: "Training Mistral MoE model for economic with router-only training"

# # DeepSpeed configuration
# deepspeed:
#   enabled: true
#   config_path: "conf/deepspeed_config.json"

# Model configuration
model:
  model_type: "mistral"
  num_experts_per_tok: 1
  base_model: "mistralai/Mistral-7B-v0.1"
  experts:
    - expert_name: "adapter_1"
      model_id: "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic"
    - expert_name: "adapter_2"
      model_id: "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa"
    # Uncomment to add more experts
    # - expert_name: "adapter_3"
    #   model_id: "predibase/customer_support_orders"
    # - expert_name: "adapter_4"
    #   model_id: "predibase/customer_support_payments"
  # router_layers: [ "gate_proj", "up_proj", "down_proj"]

# Training configuration
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 1e-4
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: "steps"
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: "steps"
  save_strategy: "steps"
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  max_seq_length: 512

# GRPO configuration (Group Relative Policy Optimization)
grpo:
  enabled: false  # Set to true to enable GRPO training instead of SFT
  per_device_train_batch_size: 1  # Can be different from SFT batch size
  per_device_eval_batch_size: 1
  learning_rate: 5e-6  # Typically lower than SFT learning rate
  num_train_epochs: 3  # Usually fewer epochs than SFT
  num_generations: 4  # Number of completions to generate per prompt
  max_completion_length: 256  # Maximum length of generated completions
  max_prompt_length: 512  # Maximum length of input prompts
  temperature: 0.7  # Temperature for generation (0.0 = deterministic, 1.0 = random)
  beta: 0.0  # KL divergence coefficient (0.0 = no KL penalty)

# Llama Guard configuration for safety assessment
llama_guard:
  enabled: true  # Use actual Llama Guard model (if false, uses heuristic fallback)
  model_id: "meta-llama/Llama-Guard-3-8B"  # Llama Guard model to use
  device_map: "auto"  # Device mapping for Llama Guard model
  torch_dtype: "bfloat16"  # Data type for Llama Guard model

# Data configuration
data:
  dataset_name: "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json"
  #"/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_cybercrime_polished.json"
  #"/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json" #"bitext/Bitext-customer-support-llm-chatbot-training-dataset"
  test_size: 0.1  # 90% train, 10% test + validation
  test_valid_split: 0.7  # Split the 10% into 0.7 test, 0.3 valid

# Paths configuration
paths:
  model_checkpoint: "data/mistral_lora_moe_economic_top1"
  output_dir: "data/output/mistral_lora_moe_economic_top1"

# Hydra configuration
hydra:
  run:
    dir: outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}
  job:
    name: ${experiment_name}
