{"fp16": {"enabled": false}, "bf16": {"enabled": true}, "optimizer": {"type": "AdamW", "params": {"lr": "auto", "betas": "auto", "eps": "auto", "weight_decay": "auto"}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": "auto", "warmup_max_lr": "auto", "warmup_num_steps": "auto"}}, "zero_optimization": {"stage": 1, "reduce_bucket_size": 50000000.0, "allgather_bucket_size": 50000000.0}, "gradient_accumulation_steps": "auto", "gradient_clipping": "auto", "steps_per_print": 25, "train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "wall_clock_breakdown": false}