{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.9195402298850575, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 6.730511665344238, "learning_rate": 0.00019969278033794163, "loss": 1.8935, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.5657082796096802, "learning_rate": 0.0001969278033794163, "loss": 1.4672, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.5766236782073975, "learning_rate": 0.00019385560675883257, "loss": 1.36, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.6168256998062134, "learning_rate": 0.00019078341013824886, "loss": 1.328, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.557741403579712, "learning_rate": 0.00018771121351766513, "loss": 1.2746, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.598112940788269, "learning_rate": 0.00018463901689708142, "loss": 1.2597, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.7861971855163574, "learning_rate": 0.0001815668202764977, "loss": 1.1986, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.8582189083099365, "learning_rate": 0.00017849462365591398, "loss": 1.1699, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.7495287656784058, "learning_rate": 0.00017542242703533028, "loss": 1.1587, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.61403226852417, "learning_rate": 0.00017235023041474657, "loss": 1.1222, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.9087177515029907, "learning_rate": 0.00016927803379416284, "loss": 1.1039, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.7650628089904785, "learning_rate": 0.0001662058371735791, "loss": 1.0793, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.8121824264526367, "learning_rate": 0.0001631336405529954, "loss": 1.0573, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.8553410768508911, "learning_rate": 0.0001600614439324117, "loss": 1.0357, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.8795347213745117, "learning_rate": 0.00015698924731182796, "loss": 1.05, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.858596682548523, "learning_rate": 0.00015391705069124425, "loss": 1.0695, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 1.9727743864059448, "learning_rate": 0.00015084485407066054, "loss": 0.9882, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.8763821125030518, "learning_rate": 0.0001477726574500768, "loss": 0.997, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 2.00811505317688, "learning_rate": 0.0001447004608294931, "loss": 0.986, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 2.098773241043091, "learning_rate": 0.00014162826420890937, "loss": 0.9247, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 2.146308183670044, "learning_rate": 0.00013855606758832566, "loss": 0.9681, "step": 200}], "logging_steps": 10, "max_steps": 651, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 5.84169130524672e+16, "train_batch_size": 16, "trial_name": null, "trial_params": null}