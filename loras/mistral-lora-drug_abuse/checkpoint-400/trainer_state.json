{"best_metric": null, "best_model_checkpoint": null, "epoch": 1.839080459770115, "eval_steps": 500, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 6.730511665344238, "learning_rate": 0.00019969278033794163, "loss": 1.8935, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.5657082796096802, "learning_rate": 0.0001969278033794163, "loss": 1.4672, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.5766236782073975, "learning_rate": 0.00019385560675883257, "loss": 1.36, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.6168256998062134, "learning_rate": 0.00019078341013824886, "loss": 1.328, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.557741403579712, "learning_rate": 0.00018771121351766513, "loss": 1.2746, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.598112940788269, "learning_rate": 0.00018463901689708142, "loss": 1.2597, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.7861971855163574, "learning_rate": 0.0001815668202764977, "loss": 1.1986, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.8582189083099365, "learning_rate": 0.00017849462365591398, "loss": 1.1699, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.7495287656784058, "learning_rate": 0.00017542242703533028, "loss": 1.1587, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.61403226852417, "learning_rate": 0.00017235023041474657, "loss": 1.1222, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.9087177515029907, "learning_rate": 0.00016927803379416284, "loss": 1.1039, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.7650628089904785, "learning_rate": 0.0001662058371735791, "loss": 1.0793, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.8121824264526367, "learning_rate": 0.0001631336405529954, "loss": 1.0573, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.8553410768508911, "learning_rate": 0.0001600614439324117, "loss": 1.0357, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.8795347213745117, "learning_rate": 0.00015698924731182796, "loss": 1.05, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.858596682548523, "learning_rate": 0.00015391705069124425, "loss": 1.0695, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 1.9727743864059448, "learning_rate": 0.00015084485407066054, "loss": 0.9882, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.8763821125030518, "learning_rate": 0.0001477726574500768, "loss": 0.997, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 2.00811505317688, "learning_rate": 0.0001447004608294931, "loss": 0.986, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 2.098773241043091, "learning_rate": 0.00014162826420890937, "loss": 0.9247, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 2.146308183670044, "learning_rate": 0.00013855606758832566, "loss": 0.9681, "step": 200}, {"epoch": 0.9655172413793104, "grad_norm": 2.272186756134033, "learning_rate": 0.00013548387096774193, "loss": 0.906, "step": 210}, {"epoch": 1.0114942528735633, "grad_norm": 2.2534825801849365, "learning_rate": 0.00013241167434715822, "loss": 0.7652, "step": 220}, {"epoch": 1.0574712643678161, "grad_norm": 2.1146228313446045, "learning_rate": 0.00012933947772657452, "loss": 0.6047, "step": 230}, {"epoch": 1.103448275862069, "grad_norm": 2.499401807785034, "learning_rate": 0.0001262672811059908, "loss": 0.5948, "step": 240}, {"epoch": 1.1494252873563218, "grad_norm": 2.232330560684204, "learning_rate": 0.00012319508448540708, "loss": 0.5759, "step": 250}, {"epoch": 1.1954022988505748, "grad_norm": 2.316417694091797, "learning_rate": 0.00012012288786482336, "loss": 0.5534, "step": 260}, {"epoch": 1.2413793103448276, "grad_norm": 2.285057544708252, "learning_rate": 0.00011705069124423964, "loss": 0.535, "step": 270}, {"epoch": 1.2873563218390804, "grad_norm": 2.3081932067871094, "learning_rate": 0.00011397849462365593, "loss": 0.5721, "step": 280}, {"epoch": 1.3333333333333333, "grad_norm": 2.4331536293029785, "learning_rate": 0.0001109062980030722, "loss": 0.5427, "step": 290}, {"epoch": 1.3793103448275863, "grad_norm": 2.5799248218536377, "learning_rate": 0.00010783410138248849, "loss": 0.5085, "step": 300}, {"epoch": 1.4252873563218391, "grad_norm": 2.3844504356384277, "learning_rate": 0.00010476190476190477, "loss": 0.5303, "step": 310}, {"epoch": 1.471264367816092, "grad_norm": 2.5422866344451904, "learning_rate": 0.00010168970814132104, "loss": 0.5319, "step": 320}, {"epoch": 1.5172413793103448, "grad_norm": 2.3469738960266113, "learning_rate": 9.861751152073733e-05, "loss": 0.5081, "step": 330}, {"epoch": 1.5632183908045976, "grad_norm": 2.763138771057129, "learning_rate": 9.554531490015361e-05, "loss": 0.4973, "step": 340}, {"epoch": 1.6091954022988506, "grad_norm": 2.468764305114746, "learning_rate": 9.247311827956989e-05, "loss": 0.4695, "step": 350}, {"epoch": 1.6551724137931034, "grad_norm": 2.398834228515625, "learning_rate": 8.940092165898618e-05, "loss": 0.4912, "step": 360}, {"epoch": 1.7011494252873565, "grad_norm": 2.1900534629821777, "learning_rate": 8.632872503840246e-05, "loss": 0.4755, "step": 370}, {"epoch": 1.7471264367816093, "grad_norm": 2.034898519515991, "learning_rate": 8.325652841781874e-05, "loss": 0.4417, "step": 380}, {"epoch": 1.793103448275862, "grad_norm": 2.3716275691986084, "learning_rate": 8.018433179723502e-05, "loss": 0.4553, "step": 390}, {"epoch": 1.839080459770115, "grad_norm": 2.673387289047241, "learning_rate": 7.711213517665132e-05, "loss": 0.4807, "step": 400}], "logging_steps": 10, "max_steps": 651, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.16760848827392e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}